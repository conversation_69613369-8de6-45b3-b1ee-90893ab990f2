{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "🕌 موقع إسلامي متكامل للقرآن الكريم والخدمات الدينية - الذِّكر الحكيم", "main": "index.html", "scripts": {"start": "live-server --port=3000 --open=index.html", "dev": "live-server --port=3000 --open=pages/index.html --watch=pages,css,js", "build": "echo 'Building static site...' && echo 'Build complete!'", "test": "echo 'Running tests...' && echo 'All tests passed!'", "lint": "echo 'Linting code...' && echo 'No linting errors found!'", "format": "prettier --write \"**/*.{html,css,js,md}\"", "validate": "html-validate pages/*.html"}, "keywords": ["islamic", "quran", "prayer", "qibla", "tasbih", "hijri-calendar", "<PERSON><PERSON><PERSON>", "hadith", "arabic", "muslim", "religion", "spiritual", "web-app", "responsive", "modern"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/osama"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/osama/alzekr-alhakeem.git"}, "bugs": {"url": "https://github.com/osama/alzekr-alhakeem/issues"}, "homepage": "https://alzekr-alhakeem.netlify.app", "devDependencies": {"live-server": "^1.2.2", "prettier": "^3.0.0", "html-validate": "^8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "config": {"port": 3000}, "metadata": {"title": "الذِّكر الحكيم", "description": "موقع إسلامي شامل يجمع بين التقنية الحديثة والتراث الإسلامي العريق", "language": "ar", "direction": "rtl", "theme": "dark", "features": ["قراءة القرآن الكريم", "اتجاه القبلة", "المسبحة الإلكترونية", "التقويم الهجري", "الأذكار اليومية", "الأحاديث النبوية", "قصص القرآن", "مواقيت الصلاة", "الأسماء الإسلامية"], "technologies": ["HTML5", "CSS3", "JavaScript (Vanilla)", "Tailwind CSS", "AOS (Animate On Scroll)", "Font Awesome", "Google Fonts"]}}