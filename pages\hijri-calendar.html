<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الذِّكر الحكيم - التقويم الهجري</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    
    <style>
        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-glass {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            overflow: hidden;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(15, 23, 42, 0.8);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .calendar-day:hover {
            background: rgba(16, 185, 129, 0.2);
        }
        
        .calendar-day.today {
            background: linear-gradient(135deg, #10b981, #34d399);
            color: white;
            font-weight: bold;
        }
        
        .calendar-day.other-month {
            opacity: 0.3;
        }
        
        .calendar-header {
            background: rgba(16, 185, 129, 0.2);
            color: #34d399;
            font-weight: bold;
            padding: 1rem;
        }
        
        .event-dot {
            position: absolute;
            bottom: 4px;
            right: 4px;
            width: 6px;
            height: 6px;
            background: #f59e0b;
            border-radius: 50%;
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    
    <!-- Navigation Bar -->
    <nav class="navbar-glass fixed top-0 w-full z-50 py-4">
        <div class="container mx-auto px-6 flex items-center justify-between">
            <!-- Logo and Site Name -->
            <div class="flex items-center space-x-4 space-x-reverse cursor-pointer" onclick="goToHome()">
                <div class="text-3xl text-emerald-400">
                    <i class="fas fa-mosque"></i>
                </div>
                <div class="text-right">
                    <h1 class="text-2xl font-bold text-white">الذِّكر الحكيم</h1>
                    <p class="text-sm text-emerald-300 font-light">وننزل من القرآن ما هو شفاء ورحمة للمؤمنين</p>
                </div>
            </div>
            
            <!-- Back Button -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <button onclick="goBack()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-full transition-colors duration-300">
                    <i class="fas fa-arrow-right ml-2"></i>عودة
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-32 pb-16">
        <!-- Page Header -->
        <section class="container mx-auto px-6 text-center mb-16">
            <div data-aos="fade-up" data-aos-duration="1000">
                <div class="text-6xl text-emerald-400 mb-6">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 text-emerald-400">
                    التقويم الهجري
                </h2>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
                    تابع التاريخ الهجري والمناسبات الإسلامية
                </p>
                <div class="w-24 h-1 bg-emerald-400 mx-auto rounded-full"></div>
            </div>
        </section>
        
        <!-- Current Date Display -->
        <section class="container mx-auto px-6 mb-12">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                <div class="glass-effect rounded-3xl p-8 text-center">
                    <h3 class="text-2xl font-bold text-white mb-6">التاريخ اليوم</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="bg-emerald-500 bg-opacity-20 rounded-xl p-6">
                            <h4 class="text-emerald-300 font-semibold mb-4">التاريخ الهجري</h4>
                            <p class="text-3xl font-bold text-white mb-2" id="hijri-date">
                                ١ محرم ١٤٤٦ هـ
                            </p>
                            <p class="text-emerald-300 text-sm" id="hijri-day">
                                الأحد
                            </p>
                        </div>
                        <div class="bg-blue-500 bg-opacity-20 rounded-xl p-6">
                            <h4 class="text-blue-300 font-semibold mb-4">التاريخ الميلادي</h4>
                            <p class="text-3xl font-bold text-white mb-2" id="gregorian-date">
                                7 يوليو 2024 م
                            </p>
                            <p class="text-blue-300 text-sm" id="gregorian-day">
                                الأحد
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Calendar Navigation -->
        <section class="container mx-auto px-6 mb-8">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="300">
                <div class="glass-effect rounded-2xl p-6">
                    <div class="flex items-center justify-between">
                        <button onclick="previousMonth()" class="bg-emerald-500 hover:bg-emerald-600 text-white p-3 rounded-full transition-colors duration-300">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        
                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-white" id="current-month">
                                محرم ١٤٤٦ هـ
                            </h3>
                            <p class="text-emerald-300 text-sm" id="current-month-gregorian">
                                يوليو 2024 م
                            </p>
                        </div>
                        
                        <button onclick="nextMonth()" class="bg-emerald-500 hover:bg-emerald-600 text-white p-3 rounded-full transition-colors duration-300">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Calendar Grid -->
        <section class="container mx-auto px-6 mb-16">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="400">
                <div class="glass-effect rounded-2xl p-6">
                    <!-- Days of Week Header -->
                    <div class="calendar-grid mb-1">
                        <div class="calendar-header">الأحد</div>
                        <div class="calendar-header">الاثنين</div>
                        <div class="calendar-header">الثلاثاء</div>
                        <div class="calendar-header">الأربعاء</div>
                        <div class="calendar-header">الخميس</div>
                        <div class="calendar-header">الجمعة</div>
                        <div class="calendar-header">السبت</div>
                    </div>
                    
                    <!-- Calendar Days -->
                    <div class="calendar-grid" id="calendar-days">
                        <!-- Days will be generated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Islamic Events -->
        <section class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="600">
                <h3 class="text-2xl font-bold text-center text-white mb-8">المناسبات الإسلامية القادمة</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center space-x-4 space-x-reverse mb-4">
                            <div class="bg-yellow-500 text-white w-12 h-12 rounded-full flex items-center justify-center">
                                <i class="fas fa-star"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-white">عاشوراء</h4>
                                <p class="text-gray-400 text-sm">10 محرم 1446 هـ</p>
                            </div>
                        </div>
                        <p class="text-gray-300 text-sm">
                            يوم عاشوراء هو اليوم العاشر من شهر محرم، وهو يوم مبارك يُستحب فيه الصيام
                        </p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center space-x-4 space-x-reverse mb-4">
                            <div class="bg-green-500 text-white w-12 h-12 rounded-full flex items-center justify-center">
                                <i class="fas fa-mosque"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-white">المولد النبوي</h4>
                                <p class="text-gray-400 text-sm">12 ربيع الأول 1446 هـ</p>
                            </div>
                        </div>
                        <p class="text-gray-300 text-sm">
                            ذكرى مولد النبي محمد صلى الله عليه وسلم، خير البشر وخاتم المرسلين
                        </p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center space-x-4 space-x-reverse mb-4">
                            <div class="bg-purple-500 text-white w-12 h-12 rounded-full flex items-center justify-center">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-white">ليلة الإسراء والمعراج</h4>
                                <p class="text-gray-400 text-sm">27 رجب 1446 هـ</p>
                            </div>
                        </div>
                        <p class="text-gray-300 text-sm">
                            ذكرى رحلة الإسراء والمعراج المباركة للنبي محمد صلى الله عليه وسلم
                        </p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6">
                        <div class="flex items-center space-x-4 space-x-reverse mb-4">
                            <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center">
                                <i class="fas fa-crescent-moon"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-white">بداية رمضان</h4>
                                <p class="text-gray-400 text-sm">1 رمضان 1446 هـ</p>
                            </div>
                        </div>
                        <p class="text-gray-300 text-sm">
                            بداية شهر رمضان المبارك، شهر الصيام والقيام وتلاوة القرآن
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-slate-800 bg-opacity-50 backdrop-blur-lg border-t border-gray-700 py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
                    <i class="fas fa-mosque text-2xl text-emerald-400"></i>
                    <h4 class="text-2xl font-bold text-white">الذِّكر الحكيم</h4>
                </div>
                <p class="text-emerald-300 text-lg mb-6 font-light">
                    "اللهم اجعل هذا العمل خالصًا لوجهك الكريم"
                </p>
                <div class="border-t border-gray-600 pt-6">
                    <p class="text-gray-400 mb-4">
                        © 2024 الذِّكر الحكيم - جميع الحقوق محفوظة
                    </p>
                    <div class="flex items-center justify-center space-x-6 space-x-reverse">
                        <span class="text-gray-300">تطوير: Osama Developer</span>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="mailto:<EMAIL>" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://github.com/osama" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://wa.me/1234567890" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="../js/script.js"></script>
    <script>
        // Hijri months
        const hijriMonths = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
            'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ];
        
        const gregorianMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        
        const arabicDays = [
            'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
        ];
        
        let currentHijriYear = 1446;
        let currentHijriMonth = 0; // محرم
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
            
            updateCurrentDate();
            generateCalendar();
        });
        
        function updateCurrentDate() {
            const today = new Date();
            const hijriDate = convertToHijri(today);
            
            // Update Hijri date
            document.getElementById('hijri-date').textContent = 
                `${toArabicNumber(hijriDate.day)} ${hijriMonths[hijriDate.month - 1]} ${toArabicNumber(hijriDate.year)} هـ`;
            document.getElementById('hijri-day').textContent = arabicDays[today.getDay()];
            
            // Update Gregorian date
            document.getElementById('gregorian-date').textContent = 
                `${today.getDate()} ${gregorianMonths[today.getMonth()]} ${today.getFullYear()} م`;
            document.getElementById('gregorian-day').textContent = arabicDays[today.getDay()];
        }
        
        function generateCalendar() {
            const calendarDays = document.getElementById('calendar-days');
            calendarDays.innerHTML = '';
            
            // Update month header
            document.getElementById('current-month').textContent = 
                `${hijriMonths[currentHijriMonth]} ${toArabicNumber(currentHijriYear)} هـ`;
            
            // Generate calendar days (simplified - 30 days per month)
            const daysInMonth = 30;
            const startDay = 0; // Assuming month starts on Sunday for simplicity
            
            // Add empty cells for days before month start
            for (let i = 0; i < startDay; i++) {
                const dayCell = document.createElement('div');
                dayCell.className = 'calendar-day other-month';
                calendarDays.appendChild(dayCell);
            }
            
            // Add days of the month
            for (let day = 1; day <= daysInMonth; day++) {
                const dayCell = document.createElement('div');
                dayCell.className = 'calendar-day';
                dayCell.textContent = toArabicNumber(day);
                
                // Mark today (simplified)
                if (day === 7 && currentHijriMonth === 0 && currentHijriYear === 1446) {
                    dayCell.classList.add('today');
                }
                
                // Add event dots for special days
                if (day === 10 && currentHijriMonth === 0) { // عاشوراء
                    const eventDot = document.createElement('div');
                    eventDot.className = 'event-dot';
                    dayCell.appendChild(eventDot);
                }
                
                dayCell.onclick = () => showDayInfo(day);
                calendarDays.appendChild(dayCell);
            }
        }
        
        function previousMonth() {
            if (currentHijriMonth === 0) {
                currentHijriMonth = 11;
                currentHijriYear--;
            } else {
                currentHijriMonth--;
            }
            generateCalendar();
        }
        
        function nextMonth() {
            if (currentHijriMonth === 11) {
                currentHijriMonth = 0;
                currentHijriYear++;
            } else {
                currentHijriMonth++;
            }
            generateCalendar();
        }
        
        function showDayInfo(day) {
            const monthName = hijriMonths[currentHijriMonth];
            showNotification(`${toArabicNumber(day)} ${monthName} ${toArabicNumber(currentHijriYear)} هـ`, 'info');
        }
        
        function convertToHijri(gregorianDate) {
            // Simplified Hijri conversion (approximate)
            // In a real application, you would use a proper Hijri calendar library
            const hijriEpoch = new Date(622, 6, 16); // Approximate Hijri epoch
            const daysDiff = Math.floor((gregorianDate - hijriEpoch) / (1000 * 60 * 60 * 24));
            const hijriYear = Math.floor(daysDiff / 354) + 1; // Approximate
            const dayOfYear = daysDiff % 354;
            const hijriMonth = Math.floor(dayOfYear / 30) + 1;
            const hijriDay = (dayOfYear % 30) + 1;
            
            return {
                year: hijriYear,
                month: hijriMonth,
                day: hijriDay
            };
        }
        
        function toArabicNumber(number) {
            const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return number.toString().replace(/[0-9]/g, function(w) {
                return arabicNumbers[+w];
            });
        }
        
        function goToHome() {
            window.location.href = 'index.html';
        }
        
        function goBack() {
            window.history.back();
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-20 right-6 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            if (type === 'success') {
                notification.classList.add('bg-emerald-500', 'text-white');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
            }
            
            notification.innerHTML = `
                <div class="flex items-center space-x-3 space-x-reverse">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
