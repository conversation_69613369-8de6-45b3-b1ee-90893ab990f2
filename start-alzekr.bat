@echo off
echo 🕌 تشغيل موقع الذِّكر الحكيم...
echo.
echo اختر طريقة التشغيل:
echo 1. فتح الصفحة الرئيسية
echo 2. فتح صفحة البداية
echo 3. فتح صفحة الخدمات
echo 4. فتح قراءة القرآن
echo 5. فتح اتجاه القبلة
echo 6. فتح المسبحة الإلكترونية
echo.
set /p choice="اختر رقم (1-6): "

if "%choice%"=="1" (
    start "" "pages\index.html"
) else if "%choice%"=="2" (
    start "" "alzekr-start.html"
) else if "%choice%"=="3" (
    start "" "pages\services.html"
) else if "%choice%"=="4" (
    start "" "pages\quran-reading.html"
) else if "%choice%"=="5" (
    start "" "pages\qibla-direction.html"
) else if "%choice%"=="6" (
    start "" "pages\digital-tasbih.html"
) else (
    echo خيار غير صحيح، سيتم فتح الصفحة الرئيسية...
    start "" "pages\index.html"
)

echo.
echo تم فتح الموقع في المتصفح الافتراضي
pause
