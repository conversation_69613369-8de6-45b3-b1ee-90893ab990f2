/* الذِّكر الحكيم - Custom Styles */
/* تطوير: <PERSON><PERSON><PERSON> */

/* ========================================
   Arabic Fonts Import
   ======================================== */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&family=Lateef:wght@400;700&display=swap');

/* ========================================
   CSS Variables
   ======================================== */
:root {
    --primary-color: #10b981;
    --primary-dark: #059669;
    --secondary-color: #1e293b;
    --accent-color: #34d399;
    --text-light: #f1f5f9;
    --text-gray: #94a3b8;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-emerald: rgba(16, 185, 129, 0.25);
    
    /* Arabic Font Stack */
    --font-arabic: 'Cairo', 'Amiri', 'Lateef', 'Tahoma', sans-serif;
    --font-quran: 'Amiri', 'Lateef', 'Traditional Arabic', serif;
}

/* ========================================
   Base Styles
   ======================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-arabic);
    line-height: 1.6;
    color: var(--text-light);
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ========================================
   Typography
   ======================================== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-arabic);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.font-amiri {
    font-family: var(--font-quran);
}

.text-quran {
    font-family: var(--font-quran);
    font-size: 1.25rem;
    line-height: 2;
    text-align: center;
    color: var(--accent-color);
}

/* ========================================
   Glass Morphism Effects
   ======================================== */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-strong {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* ========================================
   Navigation Styles
   ======================================== */
.navbar-glass {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.nav-link {
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.nav-link:hover {
    background: rgba(16, 185, 129, 0.1);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

/* ========================================
   Button Styles
   ======================================== */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 15px var(--shadow-emerald);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-emerald);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* ========================================
   Card Styles
   ======================================== */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    padding: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.2);
    border-color: var(--primary-color);
}

.service-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(16, 185, 129, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.service-card:hover::before {
    opacity: 1;
}

/* ========================================
   Animation Classes
   ======================================== */
.fade-in {
    animation: fadeIn 0.8s ease-in-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ========================================
   Islamic Elements
   ======================================== */
.islamic-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(52, 211, 153, 0.1) 0%, transparent 50%);
}

.crescent-decoration::before {
    content: '☪';
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-left: 0.5rem;
}

.verse-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(52, 211, 153, 0.05));
    border-right: 4px solid var(--primary-color);
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}

/* ========================================
   Responsive Design
   ======================================== */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card {
        padding: 1.5rem;
    }
    
    .text-quran {
        font-size: 1.1rem;
    }
    
    .btn-primary {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 13px;
    }
    
    .card {
        padding: 1rem;
    }
    
    .text-quran {
        font-size: 1rem;
        line-height: 1.8;
    }
}

/* ========================================
   Utility Classes
   ======================================== */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(135deg, var(--glass-bg), var(--glass-bg)) padding-box,
                linear-gradient(135deg, var(--primary-color), var(--accent-color)) border-box;
}

.glow-effect {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* ========================================
   Loading Animation
   ======================================== */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(16, 185, 129, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========================================
   Scrollbar Styling
   ======================================== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* ========================================
   Print Styles
   ======================================== */
@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .glass-effect {
        background: white !important;
        border: 1px solid #ccc !important;
    }
    
    .navbar-glass {
        display: none !important;
    }
}

/* ========================================
   Accessibility
   ======================================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ========================================
   Focus Styles
   ======================================== */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ========================================
   High Contrast Mode
   ======================================== */
@media (prefers-contrast: high) {
    .glass-effect {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid var(--primary-color);
    }
    
    .text-gray-300 {
        color: #ffffff !important;
    }
}
