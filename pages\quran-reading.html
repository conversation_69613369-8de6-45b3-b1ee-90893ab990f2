<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الذِّكر الحكيم - قراءة القرآن الكريم</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    
    <style>
        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-glass {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .surah-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .surah-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(16, 185, 129, 0.2);
        }
        
        .verse-text {
            font-family: 'Amiri', serif;
            font-size: 1.5rem;
            line-height: 2.5;
            text-align: center;
            color: #34d399;
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    
    <!-- Navigation Bar -->
    <nav class="navbar-glass fixed top-0 w-full z-50 py-4">
        <div class="container mx-auto px-6 flex items-center justify-between">
            <!-- Logo and Site Name -->
            <div class="flex items-center space-x-4 space-x-reverse cursor-pointer" onclick="goToHome()">
                <div class="text-3xl text-emerald-400">
                    <i class="fas fa-mosque"></i>
                </div>
                <div class="text-right">
                    <h1 class="text-2xl font-bold text-white">الذِّكر الحكيم</h1>
                    <p class="text-sm text-emerald-300 font-light">وننزل من القرآن ما هو شفاء ورحمة للمؤمنين</p>
                </div>
            </div>
            
            <!-- Back Button -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <button onclick="goBack()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-full transition-colors duration-300">
                    <i class="fas fa-arrow-right ml-2"></i>عودة
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-32 pb-16">
        <!-- Page Header -->
        <section class="container mx-auto px-6 text-center mb-16">
            <div data-aos="fade-up" data-aos-duration="1000">
                <div class="text-6xl text-emerald-400 mb-6">
                    <i class="fas fa-book-open"></i>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 text-emerald-400">
                    قراءة القرآن الكريم
                </h2>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
                    اقرأ واستمع إلى كلام الله العظيم
                </p>
                <div class="w-24 h-1 bg-emerald-400 mx-auto rounded-full"></div>
            </div>
        </section>
        
        <!-- Search Section -->
        <section class="container mx-auto px-6 mb-12">
            <div class="max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                <div class="glass-effect rounded-2xl p-6">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" id="surah-search" placeholder="ابحث عن سورة..." 
                                   class="w-full bg-slate-800 text-white px-4 py-3 rounded-lg border border-gray-600 focus:border-emerald-400 focus:outline-none transition-colors">
                        </div>
                        <button onclick="searchSurah()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-3 rounded-lg transition-colors duration-300">
                            <i class="fas fa-search ml-2"></i>بحث
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Featured Verse -->
        <section class="container mx-auto px-6 mb-16">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="300">
                <div class="glass-effect rounded-3xl p-8 md:p-12 text-center">
                    <h3 class="text-2xl font-bold text-white mb-6">آية اليوم</h3>
                    <div class="verse-text mb-6">
                        "وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ وَلَا يَزِيدُ الظَّالِمِينَ إِلَّا خَسَارًا"
                    </div>
                    <p class="text-emerald-300 font-semibold">سورة الإسراء - آية 82</p>
                </div>
            </div>
        </section>
        
        <!-- Surahs Grid -->
        <section class="container mx-auto px-6">
            <h3 class="text-3xl font-bold text-center text-white mb-12" data-aos="fade-up">
                سور القرآن الكريم
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="surahs-container">
                <!-- Surahs will be loaded here dynamically -->
            </div>
        </section>
        
        <!-- Quick Access -->
        <section class="container mx-auto px-6 mt-16">
            <div class="max-w-4xl mx-auto" data-aos="fade-up">
                <h3 class="text-2xl font-bold text-center text-white mb-8">وصول سريع</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button onclick="openSurah(1)" class="glass-effect rounded-xl p-4 text-center hover:bg-emerald-500 hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-star text-2xl text-emerald-400 mb-2"></i>
                        <p class="text-white font-semibold">الفاتحة</p>
                    </button>
                    <button onclick="openSurah(2)" class="glass-effect rounded-xl p-4 text-center hover:bg-emerald-500 hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-book text-2xl text-emerald-400 mb-2"></i>
                        <p class="text-white font-semibold">البقرة</p>
                    </button>
                    <button onclick="openSurah(36)" class="glass-effect rounded-xl p-4 text-center hover:bg-emerald-500 hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-heart text-2xl text-emerald-400 mb-2"></i>
                        <p class="text-white font-semibold">يس</p>
                    </button>
                    <button onclick="openSurah(67)" class="glass-effect rounded-xl p-4 text-center hover:bg-emerald-500 hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-crown text-2xl text-emerald-400 mb-2"></i>
                        <p class="text-white font-semibold">الملك</p>
                    </button>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-slate-800 bg-opacity-50 backdrop-blur-lg border-t border-gray-700 py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
                    <i class="fas fa-mosque text-2xl text-emerald-400"></i>
                    <h4 class="text-2xl font-bold text-white">الذِّكر الحكيم</h4>
                </div>
                <p class="text-emerald-300 text-lg mb-6 font-light">
                    "اللهم اجعل هذا العمل خالصًا لوجهك الكريم"
                </p>
                <div class="border-t border-gray-600 pt-6">
                    <p class="text-gray-400 mb-4">
                        © 2024 الذِّكر الحكيم - جميع الحقوق محفوظة
                    </p>
                    <div class="flex items-center justify-center space-x-6 space-x-reverse">
                        <span class="text-gray-300">تطوير: Osama Developer</span>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="mailto:<EMAIL>" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://github.com/osama" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://wa.me/1234567890" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="../js/script.js"></script>
    <script>
        // Quran data (simplified - first 10 surahs)
        const surahs = [
            { number: 1, name: "الفاتحة", englishName: "Al-Fatiha", verses: 7, type: "مكية" },
            { number: 2, name: "البقرة", englishName: "Al-Baqarah", verses: 286, type: "مدنية" },
            { number: 3, name: "آل عمران", englishName: "Aal-E-Imran", verses: 200, type: "مدنية" },
            { number: 4, name: "النساء", englishName: "An-Nisa", verses: 176, type: "مدنية" },
            { number: 5, name: "المائدة", englishName: "Al-Maidah", verses: 120, type: "مدنية" },
            { number: 6, name: "الأنعام", englishName: "Al-An'am", verses: 165, type: "مكية" },
            { number: 7, name: "الأعراف", englishName: "Al-A'raf", verses: 206, type: "مكية" },
            { number: 8, name: "الأنفال", englishName: "Al-Anfal", verses: 75, type: "مدنية" },
            { number: 9, name: "التوبة", englishName: "At-Tawbah", verses: 129, type: "مدنية" },
            { number: 10, name: "يونس", englishName: "Yunus", verses: 109, type: "مكية" }
        ];
        
        // Load surahs on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSurahs();
            
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        });
        
        function loadSurahs() {
            const container = document.getElementById('surahs-container');
            container.innerHTML = '';
            
            surahs.forEach((surah, index) => {
                const surahCard = createSurahCard(surah, index);
                container.appendChild(surahCard);
            });
        }
        
        function createSurahCard(surah, index) {
            const card = document.createElement('div');
            card.className = 'surah-card glass-effect rounded-2xl p-6';
            card.setAttribute('data-aos', 'fade-up');
            card.setAttribute('data-aos-delay', (index * 100).toString());
            card.onclick = () => openSurah(surah.number);
            
            card.innerHTML = `
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-emerald-500 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
                        ${surah.number}
                    </div>
                    <div class="text-right flex-1 mr-4">
                        <h4 class="text-xl font-bold text-white">${surah.name}</h4>
                        <p class="text-gray-400 text-sm">${surah.englishName}</p>
                    </div>
                </div>
                <div class="flex justify-between items-center text-sm">
                    <span class="bg-emerald-500 bg-opacity-20 text-emerald-300 px-3 py-1 rounded-full">
                        ${surah.type}
                    </span>
                    <span class="text-gray-400">
                        ${surah.verses} آية
                    </span>
                </div>
            `;
            
            return card;
        }
        
        function searchSurah() {
            const searchTerm = document.getElementById('surah-search').value.toLowerCase();
            const filteredSurahs = surahs.filter(surah => 
                surah.name.includes(searchTerm) || 
                surah.englishName.toLowerCase().includes(searchTerm)
            );
            
            const container = document.getElementById('surahs-container');
            container.innerHTML = '';
            
            if (filteredSurahs.length === 0) {
                container.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-search text-4xl text-gray-500 mb-4"></i>
                        <p class="text-gray-400 text-lg">لم يتم العثور على نتائج</p>
                    </div>
                `;
                return;
            }
            
            filteredSurahs.forEach((surah, index) => {
                const surahCard = createSurahCard(surah, index);
                container.appendChild(surahCard);
            });
        }
        
        function openSurah(surahNumber) {
            // In a real application, this would navigate to a detailed surah page
            showNotification(`سيتم فتح سورة رقم ${surahNumber} قريباً`, 'info');
        }
        
        function goToHome() {
            window.location.href = 'index.html';
        }
        
        function goBack() {
            window.history.back();
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-20 right-6 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            if (type === 'success') {
                notification.classList.add('bg-emerald-500', 'text-white');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
            }
            
            notification.innerHTML = `
                <div class="flex items-center space-x-3 space-x-reverse">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
