// الذِّكر الحكيم - Main JavaScript File
// تطوير: <PERSON><PERSON><PERSON>

// ========================================
// Global Variables
// ========================================
let currentPage = window.location.pathname.split('/').pop() || 'index.html';

// ========================================
// DOM Content Loaded Event
// ========================================
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// ========================================
// App Initialization
// ========================================
function initializeApp() {
    setupMobileMenu();
    setupSmoothScrolling();
    setupNavbarEffects();
    setupServiceCards();
    updateActiveNavLink();
    
    // Initialize AOS if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }
    
    console.log('🕌 الذِّكر الحكيم - تم تحميل الموقع بنجاح');
}

// ========================================
// Mobile Menu Functions
// ========================================
function setupMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            toggleMobileMenu();
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                closeMobileMenu();
            }
        });
        
        // Close mobile menu when clicking on a link
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMobileMenu();
            });
        });
    }
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.querySelector('#mobile-menu-btn i');
    
    if (mobileMenu.classList.contains('hidden')) {
        openMobileMenu();
    } else {
        closeMobileMenu();
    }
}

function openMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.querySelector('#mobile-menu-btn i');
    
    mobileMenu.classList.remove('hidden');
    menuIcon.classList.remove('fa-bars');
    menuIcon.classList.add('fa-times');
    
    // Add animation
    mobileMenu.style.opacity = '0';
    mobileMenu.style.transform = 'translateY(-10px)';
    
    setTimeout(() => {
        mobileMenu.style.transition = 'all 0.3s ease';
        mobileMenu.style.opacity = '1';
        mobileMenu.style.transform = 'translateY(0)';
    }, 10);
}

function closeMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.querySelector('#mobile-menu-btn i');
    
    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
        mobileMenu.style.transition = 'all 0.3s ease';
        mobileMenu.style.opacity = '0';
        mobileMenu.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
            mobileMenu.classList.add('hidden');
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
        }, 300);
    }
}

// ========================================
// Navigation Functions
// ========================================
function goToHome() {
    window.location.href = 'index.html';
}

function goToServices() {
    window.location.href = 'services.html';
}

function goToAbout() {
    window.location.href = 'about.html';
}

function goToService(serviceName) {
    // Navigate to specific service page
    window.location.href = `${serviceName}.html`;
}

// Service navigation mapping
const servicePages = {
    'quran-reading': 'quran-reading.html',
    'qibla-direction': 'qibla-direction.html',
    'digital-tasbih': 'digital-tasbih.html',
    'hijri-calendar': 'hijri-calendar.html',
    'daily-adhkar': 'daily-adhkar.html',
    'hadith': 'hadith.html',
    'quran-stories': 'quran-stories.html',
    'prayer-times': 'prayer-times.html',
    'islamic-names': 'islamic-names.html'
};

function goBack() {
    window.history.back();
}

function updateActiveNavLink() {
    const navLinks = document.querySelectorAll('.nav-link');
    const currentPath = window.location.pathname.split('/').pop();
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath || (currentPath === '' && href === 'index.html')) {
            link.classList.add('text-emerald-400', 'font-semibold');
            link.classList.remove('text-white');
        } else {
            link.classList.remove('text-emerald-400', 'font-semibold');
            link.classList.add('text-white');
        }
    });
}

// ========================================
// Navbar Effects
// ========================================
function setupNavbarEffects() {
    const navbar = document.querySelector('nav');
    
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(15, 23, 42, 0.95)';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'rgba(15, 23, 42, 0.9)';
                navbar.style.backdropFilter = 'blur(15px)';
            }
        });
    }
}

// ========================================
// Service Cards Setup
// ========================================
function setupServiceCards() {
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(16, 185, 129, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });
    });
}

// ========================================
// Smooth Scrolling
// ========================================
function setupSmoothScrolling() {
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ========================================
// Utility Functions
// ========================================
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-20 right-6 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-emerald-500', 'text-white');
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3 space-x-reverse">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, function(w) {
        return arabicNumbers[+w];
    });
}

function getCurrentHijriDate() {
    // This is a simplified function - in a real app, you'd use a proper Hijri calendar library
    const today = new Date();
    const hijriYear = today.getFullYear() - 579; // Approximate conversion
    return `${hijriYear} هـ`;
}

// ========================================
// Islamic Utilities
// ========================================
function getRandomVerse() {
    const verses = [
        {
            text: "وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ",
            reference: "سورة الإسراء - آية 82"
        },
        {
            text: "إِنَّ هَٰذَا الْقُرْآنَ يَهْدِي لِلَّتِي هِيَ أَقْوَمُ",
            reference: "سورة الإسراء - آية 9"
        },
        {
            text: "وَاذْكُر رَّبَّكَ فِي نَفْسِكَ تَضَرُّعًا وَخِيفَةً",
            reference: "سورة الأعراف - آية 205"
        }
    ];
    
    return verses[Math.floor(Math.random() * verses.length)];
}

function getRandomHadith() {
    const hadiths = [
        {
            text: "خَيْرُكُمْ مَنْ تَعَلَّمَ الْقُرْآنَ وَعَلَّمَهُ",
            reference: "رواه البخاري"
        },
        {
            text: "مَنْ قَرَأَ حَرْفًا مِنْ كِتَابِ اللَّهِ فَلَهُ بِهِ حَسَنَةٌ",
            reference: "رواه الترمذي"
        },
        {
            text: "اقْرَءُوا الْقُرْآنَ فَإِنَّهُ يَأْتِي يَوْمَ الْقِيَامَةِ شَفِيعًا لِأَصْحَابِهِ",
            reference: "رواه مسلم"
        }
    ];
    
    return hadiths[Math.floor(Math.random() * hadiths.length)];
}

// ========================================
// Error Handling
// ========================================
window.addEventListener('error', function(e) {
    console.error('خطأ في الموقع:', e.error);
    showNotification('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
});

// ========================================
// Performance Optimization
// ========================================
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced scroll handler
const debouncedScrollHandler = debounce(function() {
    // Handle scroll events here if needed
}, 100);

window.addEventListener('scroll', debouncedScrollHandler);

// ========================================
// Console Welcome Message
// ========================================
console.log(`
🕌 مرحباً بك في الذِّكر الحكيم
📖 موقع إسلامي متكامل للقرآن الكريم والخدمات الدينية
👨‍💻 تطوير: Osama Developer
🌟 نسأل الله أن يتقبل منا هذا العمل
`);

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        goToHome,
        goToServices,
        goToAbout,
        goToService,
        goBack,
        showNotification,
        formatArabicNumber,
        getCurrentHijriDate,
        getRandomVerse,
        getRandomHadith
    };
}
