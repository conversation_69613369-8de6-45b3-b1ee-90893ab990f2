<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الذِّكر الحكيم - اتجاه القبلة</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    
    <style>
        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-glass {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .compass {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981, #34d399);
            position: relative;
            margin: 0 auto;
            box-shadow: 0 0 50px rgba(16, 185, 129, 0.3);
        }
        
        .compass-inner {
            width: 280px;
            height: 280px;
            border-radius: 50%;
            background: rgba(15, 23, 42, 0.9);
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .compass-needle {
            width: 4px;
            height: 120px;
            background: linear-gradient(to top, #ef4444, #10b981);
            position: absolute;
            border-radius: 2px;
            transform-origin: bottom center;
            transition: transform 0.5s ease;
        }
        
        .compass-center {
            width: 20px;
            height: 20px;
            background: #10b981;
            border-radius: 50%;
            position: absolute;
            z-index: 10;
        }
        
        .direction-marker {
            position: absolute;
            font-weight: bold;
            color: #10b981;
        }
        
        .kaaba-icon {
            font-size: 2rem;
            color: #10b981;
            position: absolute;
            top: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    
    <!-- Navigation Bar -->
    <nav class="navbar-glass fixed top-0 w-full z-50 py-4">
        <div class="container mx-auto px-6 flex items-center justify-between">
            <!-- Logo and Site Name -->
            <div class="flex items-center space-x-4 space-x-reverse cursor-pointer" onclick="goToHome()">
                <div class="text-3xl text-emerald-400">
                    <i class="fas fa-mosque"></i>
                </div>
                <div class="text-right">
                    <h1 class="text-2xl font-bold text-white">الذِّكر الحكيم</h1>
                    <p class="text-sm text-emerald-300 font-light">وننزل من القرآن ما هو شفاء ورحمة للمؤمنين</p>
                </div>
            </div>
            
            <!-- Back Button -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <button onclick="goBack()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-full transition-colors duration-300">
                    <i class="fas fa-arrow-right ml-2"></i>عودة
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-32 pb-16">
        <!-- Page Header -->
        <section class="container mx-auto px-6 text-center mb-16">
            <div data-aos="fade-up" data-aos-duration="1000">
                <div class="text-6xl text-emerald-400 mb-6">
                    <i class="fas fa-kaaba"></i>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 text-emerald-400">
                    اتجاه القبلة
                </h2>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
                    اعرف الاتجاه الصحيح للكعبة المشرفة
                </p>
                <div class="w-24 h-1 bg-emerald-400 mx-auto rounded-full"></div>
            </div>
        </section>
        
        <!-- Location Status -->
        <section class="container mx-auto px-6 mb-12">
            <div class="max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                <div class="glass-effect rounded-2xl p-6 text-center">
                    <div id="location-status" class="flex items-center justify-center space-x-3 space-x-reverse">
                        <div class="loading-spinner"></div>
                        <span class="text-gray-300">جاري تحديد موقعك...</span>
                    </div>
                    <div id="location-info" class="hidden mt-4">
                        <p class="text-emerald-300 font-semibold">موقعك الحالي:</p>
                        <p id="coordinates" class="text-gray-300 text-sm"></p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Compass Section -->
        <section class="container mx-auto px-6 mb-16">
            <div class="max-w-4xl mx-auto text-center" data-aos="zoom-in" data-aos-delay="400">
                <div class="glass-effect rounded-3xl p-8 md:p-12">
                    <h3 class="text-3xl font-bold text-white mb-8">بوصلة القبلة</h3>
                    
                    <!-- Compass -->
                    <div class="compass mb-8">
                        <div class="compass-inner">
                            <div class="kaaba-icon">
                                <i class="fas fa-kaaba"></i>
                            </div>
                            <div class="compass-needle" id="compass-needle"></div>
                            <div class="compass-center"></div>
                            
                            <!-- Direction Markers -->
                            <div class="direction-marker" style="top: 10px; left: 50%; transform: translateX(-50%);">ش</div>
                            <div class="direction-marker" style="right: 10px; top: 50%; transform: translateY(-50%);">ق</div>
                            <div class="direction-marker" style="bottom: 10px; left: 50%; transform: translateX(-50%);">ج</div>
                            <div class="direction-marker" style="left: 10px; top: 50%; transform: translateY(-50%);">غ</div>
                        </div>
                    </div>
                    
                    <!-- Qibla Info -->
                    <div id="qibla-info" class="hidden">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div class="bg-emerald-500 bg-opacity-20 rounded-xl p-4">
                                <h4 class="text-emerald-300 font-semibold mb-2">اتجاه القبلة</h4>
                                <p id="qibla-direction" class="text-2xl font-bold text-white">--°</p>
                            </div>
                            <div class="bg-emerald-500 bg-opacity-20 rounded-xl p-4">
                                <h4 class="text-emerald-300 font-semibold mb-2">المسافة إلى مكة</h4>
                                <p id="distance-to-mecca" class="text-2xl font-bold text-white">-- كم</p>
                            </div>
                            <div class="bg-emerald-500 bg-opacity-20 rounded-xl p-4">
                                <h4 class="text-emerald-300 font-semibold mb-2">دقة البوصلة</h4>
                                <p id="compass-accuracy" class="text-2xl font-bold text-white">عالية</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="findQibla()" id="find-qibla-btn" 
                                class="bg-emerald-500 hover:bg-emerald-600 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-compass ml-2"></i>
                            تحديد اتجاه القبلة
                        </button>
                        <button onclick="calibrateCompass()" 
                                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-sync-alt ml-2"></i>
                            معايرة البوصلة
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Instructions -->
        <section class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="600">
                <div class="glass-effect rounded-3xl p-8">
                    <h3 class="text-2xl font-bold text-white mb-6 text-center">تعليمات الاستخدام</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-start space-x-4 space-x-reverse">
                            <div class="bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">1</div>
                            <div>
                                <h4 class="text-white font-semibold mb-2">السماح بالوصول للموقع</h4>
                                <p class="text-gray-300 text-sm">اسمح للموقع بالوصول إلى موقعك الجغرافي لتحديد اتجاه القبلة بدقة</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4 space-x-reverse">
                            <div class="bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">2</div>
                            <div>
                                <h4 class="text-white font-semibold mb-2">وضع الهاتف أفقياً</h4>
                                <p class="text-gray-300 text-sm">ضع هاتفك في وضع أفقي للحصول على قراءة دقيقة للبوصلة</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4 space-x-reverse">
                            <div class="bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">3</div>
                            <div>
                                <h4 class="text-white font-semibold mb-2">معايرة البوصلة</h4>
                                <p class="text-gray-300 text-sm">قم بمعايرة البوصلة بتحريك الهاتف في شكل رقم 8 عدة مرات</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4 space-x-reverse">
                            <div class="bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm">4</div>
                            <div>
                                <h4 class="text-white font-semibold mb-2">اتبع الاتجاه</h4>
                                <p class="text-gray-300 text-sm">اتبع اتجاه السهم الأخضر للوصول إلى اتجاه القبلة الصحيح</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-slate-800 bg-opacity-50 backdrop-blur-lg border-t border-gray-700 py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
                    <i class="fas fa-mosque text-2xl text-emerald-400"></i>
                    <h4 class="text-2xl font-bold text-white">الذِّكر الحكيم</h4>
                </div>
                <p class="text-emerald-300 text-lg mb-6 font-light">
                    "اللهم اجعل هذا العمل خالصًا لوجهك الكريم"
                </p>
                <div class="border-t border-gray-600 pt-6">
                    <p class="text-gray-400 mb-4">
                        © 2024 الذِّكر الحكيم - جميع الحقوق محفوظة
                    </p>
                    <div class="flex items-center justify-center space-x-6 space-x-reverse">
                        <span class="text-gray-300">تطوير: Osama Developer</span>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="mailto:<EMAIL>" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://github.com/osama" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://wa.me/1234567890" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="../js/script.js"></script>
    <script>
        // Mecca coordinates
        const MECCA_LAT = 21.4225;
        const MECCA_LNG = 39.8262;
        
        let userLocation = null;
        let deviceOrientation = 0;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
            
            // Request location permission
            requestLocation();
        });
        
        function requestLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    onLocationSuccess,
                    onLocationError,
                    { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
                );
            } else {
                showLocationError("المتصفح لا يدعم تحديد الموقع الجغرافي");
            }
        }
        
        function onLocationSuccess(position) {
            userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };
            
            updateLocationStatus(true);
            calculateQiblaDirection();
        }
        
        function onLocationError(error) {
            let message = "فشل في تحديد الموقع";
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = "تم رفض الإذن للوصول إلى الموقع";
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = "معلومات الموقع غير متاحة";
                    break;
                case error.TIMEOUT:
                    message = "انتهت مهلة طلب تحديد الموقع";
                    break;
            }
            showLocationError(message);
        }
        
        function updateLocationStatus(success) {
            const statusElement = document.getElementById('location-status');
            const infoElement = document.getElementById('location-info');
            const coordinatesElement = document.getElementById('coordinates');
            
            if (success) {
                statusElement.innerHTML = `
                    <i class="fas fa-check-circle text-emerald-400 text-xl"></i>
                    <span class="text-emerald-300">تم تحديد موقعك بنجاح</span>
                `;
                
                coordinatesElement.textContent = `خط العرض: ${userLocation.lat.toFixed(4)}°، خط الطول: ${userLocation.lng.toFixed(4)}°`;
                infoElement.classList.remove('hidden');
            } else {
                statusElement.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                    <span class="text-red-300">فشل في تحديد الموقع</span>
                `;
            }
        }
        
        function showLocationError(message) {
            updateLocationStatus(false);
            showNotification(message, 'error');
        }
        
        function calculateQiblaDirection() {
            if (!userLocation) {
                showNotification('يرجى السماح بالوصول إلى الموقع أولاً', 'error');
                return;
            }
            
            // Calculate bearing to Mecca using Haversine formula
            const lat1 = toRadians(userLocation.lat);
            const lat2 = toRadians(MECCA_LAT);
            const deltaLng = toRadians(MECCA_LNG - userLocation.lng);
            
            const y = Math.sin(deltaLng) * Math.cos(lat2);
            const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng);
            
            let bearing = toDegrees(Math.atan2(y, x));
            bearing = (bearing + 360) % 360; // Normalize to 0-360
            
            // Calculate distance
            const distance = calculateDistance(userLocation.lat, userLocation.lng, MECCA_LAT, MECCA_LNG);
            
            updateQiblaInfo(bearing, distance);
        }
        
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // Earth's radius in kilometers
            const dLat = toRadians(lat2 - lat1);
            const dLng = toRadians(lng2 - lng1);
            
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }
        
        function updateQiblaInfo(bearing, distance) {
            document.getElementById('qibla-direction').textContent = `${bearing.toFixed(1)}°`;
            document.getElementById('distance-to-mecca').textContent = `${distance.toFixed(0)} كم`;
            document.getElementById('qibla-info').classList.remove('hidden');
            
            // Update compass needle
            const needle = document.getElementById('compass-needle');
            needle.style.transform = `rotate(${bearing}deg)`;
            
            showNotification('تم تحديد اتجاه القبلة بنجاح', 'success');
        }
        
        function findQibla() {
            if (!userLocation) {
                requestLocation();
            } else {
                calculateQiblaDirection();
            }
        }
        
        function calibrateCompass() {
            showNotification('قم بتحريك هاتفك في شكل رقم 8 لمعايرة البوصلة', 'info');
            
            // Request device orientation permission (for iOS 13+)
            if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                DeviceOrientationEvent.requestPermission()
                    .then(response => {
                        if (response == 'granted') {
                            startCompassTracking();
                        }
                    })
                    .catch(console.error);
            } else {
                startCompassTracking();
            }
        }
        
        function startCompassTracking() {
            if (window.DeviceOrientationEvent) {
                window.addEventListener('deviceorientation', handleOrientation);
                showNotification('تم تفعيل تتبع البوصلة', 'success');
            } else {
                showNotification('جهازك لا يدعم البوصلة', 'error');
            }
        }
        
        function handleOrientation(event) {
            deviceOrientation = event.alpha || 0;
            // Update compass based on device orientation
            // This would be combined with the qibla direction for accurate pointing
        }
        
        function toRadians(degrees) {
            return degrees * (Math.PI / 180);
        }
        
        function toDegrees(radians) {
            return radians * (180 / Math.PI);
        }
        
        function goToHome() {
            window.location.href = 'index.html';
        }
        
        function goBack() {
            window.history.back();
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-20 right-6 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            if (type === 'success') {
                notification.classList.add('bg-emerald-500', 'text-white');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
            }
            
            notification.innerHTML = `
                <div class="flex items-center space-x-3 space-x-reverse">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
