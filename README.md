# 🕌 الذِّكر الحكيم - موقع إسلامي متكامل

موقع إسلامي شامل ومتطور يجمع بين التقنية الحديثة والتراث الإسلامي العريق، مصمم لخدمة المسلمين في جميع أنحاء العالم.

## 🌟 المميزات الرئيسية

### 📖 الخدمات الإسلامية
- **قراءة القرآن الكريم** - عرض سور وآيات القرآن مع إمكانية البحث
- **اتجاه القبلة** - بوصلة ذكية لتحديد اتجاه الكعبة المشرفة
- **المسبحة الإلكترونية** - عداد رقمي للتسبيح والاستغفار
- **التقويم الهجري** - عرض التاريخ الهجري والمناسبات الإسلامية
- **الأذكار اليومية** - أذكار الصباح والمساء
- **الأحاديث النبوية** - مجموعة مختارة من الأحاديث الصحيحة
- **قصص القرآن** - قصص الأنبياء والأحداث التاريخية
- **مواقيت الصلاة** - أوقات الصلاة حسب الموقع الجغرافي
- **الأسماء الإسلامية** - دليل الأسماء الإسلامية ومعانيها

### 🎨 التصميم والتقنية
- **تصميم عصري** - واجهة أنيقة مع تأثيرات زجاجية (Glassmorphism)
- **متجاوب بالكامل** - يعمل على جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- **الوضع الليلي** - تصميم مريح للعين في الإضاءة المنخفضة
- **حركات سلسة** - تأثيرات بصرية جذابة باستخدام AOS
- **خطوط عربية أنيقة** - استخدام خطوط Cairo وAmiri

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التنسيقات والتأثيرات البصرية
- **JavaScript (Vanilla)** - التفاعلات والوظائف الديناميكية
- **Tailwind CSS** - إطار عمل CSS للتصميم السريع
- **AOS (Animate On Scroll)** - مكتبة الحركات عند التمرير
- **Font Awesome** - مكتبة الأيقونات
- **Google Fonts** - الخطوط العربية

## 📁 هيكلية المشروع

```
alzekr_alhakeem/
│
├── index.html                 # صفحة التوجيه الرئيسية
├── README.md                  # ملف التوثيق
│
├── pages/                     # صفحات الموقع
│   ├── index.html            # الصفحة الرئيسية
│   ├── services.html         # صفحة الخدمات
│   ├── about.html            # صفحة حولنا
│   ├── quran-reading.html    # قراءة القرآن
│   ├── qibla-direction.html  # اتجاه القبلة
│   ├── digital-tasbih.html   # المسبحة الإلكترونية
│   └── [صفحات الخدمات الأخرى]
│
├── css/                      # ملفات التنسيق
│   └── styles.css           # التنسيقات المخصصة
│
├── js/                       # ملفات JavaScript
│   └── script.js            # الوظائف التفاعلية
│
└── assets/                   # الأصول والموارد
    ├── images/              # الصور
    ├── icons/               # الأيقونات
    └── fonts/               # الخطوط
```

## 🚀 كيفية التشغيل

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd alzekr_alhakeem
   ```

2. **فتح الموقع**
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي مثل Live Server

3. **للتطوير**
   - استخدم محرر نصوص مثل VS Code
   - قم بتفعيل Live Server للمعاينة المباشرة

## 📱 المتطلبات

- **متصفح حديث** يدعم HTML5 وCSS3 وJavaScript ES6+
- **اتصال بالإنترنت** لتحميل المكتبات الخارجية (Tailwind, AOS, Font Awesome)
- **إذن الموقع الجغرافي** لخدمة اتجاه القبلة ومواقيت الصلاة

## 🎯 الميزات المستقبلية

- [ ] تطبيق الهاتف المحمول (PWA)
- [ ] دعم اللغات المتعددة
- [ ] تسجيل الصوت للقرآن الكريم
- [ ] نظام التذكيرات للصلاة والأذكار
- [ ] مشاركة الآيات والأحاديث على وسائل التواصل
- [ ] وضع عدم الاتصال (Offline Mode)

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير الموقع:

1. Fork المشروع
2. إنشاء فرع جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للجميع لأغراض التعلم والتطوير.

## 👨‍💻 المطور

**Osama Developer**
- 📧 Email: <EMAIL>
- 🐙 GitHub: [@osama](https://github.com/osama)
- 📱 WhatsApp: [+1234567890](https://wa.me/1234567890)

## 🤲 دعاء

> "اللهم اجعل هذا العمل خالصًا لوجهك الكريم، وانفع به المسلمين في كل مكان"

---

© 2024 الذِّكر الحكيم - جميع الحقوق محفوظة

**"وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"**
*سورة الإسراء - آية 82*
