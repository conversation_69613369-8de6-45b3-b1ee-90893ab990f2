<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الذِّكر الحكيم - المسبحة الإلكترونية</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/styles.css">
    
    <style>
        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .navbar-glass {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .counter-circle {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981, #34d399);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
        }
        
        .counter-circle:hover {
            transform: scale(1.05);
            box-shadow: 0 0 50px rgba(16, 185, 129, 0.5);
        }
        
        .counter-circle:active {
            transform: scale(0.95);
        }
        
        .counter-inner {
            width: 220px;
            height: 220px;
            border-radius: 50%;
            background: rgba(15, 23, 42, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .dhikr-text {
            font-family: 'Amiri', serif;
            font-size: 1.5rem;
            color: #34d399;
            margin-bottom: 1rem;
            line-height: 1.8;
        }
        
        .counter-number {
            font-size: 3rem;
            font-weight: bold;
            color: white;
        }
        
        .dhikr-button {
            transition: all 0.3s ease;
        }
        
        .dhikr-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.2);
        }
        
        .dhikr-button.active {
            background: #10b981;
            color: white;
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.3s ease;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
        
        .ripple-effect {
            animation: ripple 0.6s ease-out;
        }
    </style>
</head>
<body class="bg-slate-900 text-white">
    
    <!-- Navigation Bar -->
    <nav class="navbar-glass fixed top-0 w-full z-50 py-4">
        <div class="container mx-auto px-6 flex items-center justify-between">
            <!-- Logo and Site Name -->
            <div class="flex items-center space-x-4 space-x-reverse cursor-pointer" onclick="goToHome()">
                <div class="text-3xl text-emerald-400">
                    <i class="fas fa-mosque"></i>
                </div>
                <div class="text-right">
                    <h1 class="text-2xl font-bold text-white">الذِّكر الحكيم</h1>
                    <p class="text-sm text-emerald-300 font-light">وننزل من القرآن ما هو شفاء ورحمة للمؤمنين</p>
                </div>
            </div>
            
            <!-- Back Button -->
            <div class="flex items-center space-x-4 space-x-reverse">
                <button onclick="goBack()" class="bg-emerald-500 hover:bg-emerald-600 text-white px-4 py-2 rounded-full transition-colors duration-300">
                    <i class="fas fa-arrow-right ml-2"></i>عودة
                </button>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-32 pb-16">
        <!-- Page Header -->
        <section class="container mx-auto px-6 text-center mb-16">
            <div data-aos="fade-up" data-aos-duration="1000">
                <div class="text-6xl text-emerald-400 mb-6">
                    <i class="fas fa-hand-holding-heart"></i>
                </div>
                <h2 class="text-4xl md:text-6xl font-bold mb-6 text-emerald-400">
                    المسبحة الإلكترونية
                </h2>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
                    سبح الله واذكره في كل وقت
                </p>
                <div class="w-24 h-1 bg-emerald-400 mx-auto rounded-full"></div>
            </div>
        </section>
        
        <!-- Dhikr Selection -->
        <section class="container mx-auto px-6 mb-12">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-2xl font-bold text-center text-white mb-8">اختر الذكر</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="selectDhikr('سبحان الله', 33)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400 active"
                            id="dhikr-1">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-star"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">سبحان الله</h4>
                        <p class="text-gray-400 text-sm">33 مرة</p>
                    </button>
                    
                    <button onclick="selectDhikr('الحمد لله', 33)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400"
                            id="dhikr-2">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">الحمد لله</h4>
                        <p class="text-gray-400 text-sm">33 مرة</p>
                    </button>
                    
                    <button onclick="selectDhikr('الله أكبر', 34)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400"
                            id="dhikr-3">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">الله أكبر</h4>
                        <p class="text-gray-400 text-sm">34 مرة</p>
                    </button>
                    
                    <button onclick="selectDhikr('أستغفر الله', 100)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400"
                            id="dhikr-4">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-praying-hands"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">أستغفر الله</h4>
                        <p class="text-gray-400 text-sm">100 مرة</p>
                    </button>
                    
                    <button onclick="selectDhikr('لا إله إلا الله', 100)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400"
                            id="dhikr-5">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-moon"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">لا إله إلا الله</h4>
                        <p class="text-gray-400 text-sm">100 مرة</p>
                    </button>
                    
                    <button onclick="selectDhikr('صلى الله عليه وسلم', 100)" 
                            class="dhikr-button glass-effect rounded-xl p-4 text-center border-2 border-transparent hover:border-emerald-400"
                            id="dhikr-6">
                        <div class="text-2xl text-emerald-400 mb-2">
                            <i class="fas fa-peace"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">صلى الله عليه وسلم</h4>
                        <p class="text-gray-400 text-sm">100 مرة</p>
                    </button>
                </div>
            </div>
        </section>
        
        <!-- Counter Section -->
        <section class="container mx-auto px-6 mb-16">
            <div class="max-w-2xl mx-auto text-center" data-aos="zoom-in" data-aos-delay="400">
                <div class="glass-effect rounded-3xl p-8 md:p-12">
                    <!-- Current Dhikr Display -->
                    <div class="dhikr-text mb-8" id="current-dhikr">
                        سبحان الله
                    </div>
                    
                    <!-- Progress Ring -->
                    <div class="relative mb-8">
                        <svg class="progress-ring w-64 h-64 mx-auto" width="250" height="250">
                            <circle class="progress-ring-circle" 
                                    stroke="#374151" 
                                    stroke-width="8" 
                                    fill="transparent" 
                                    r="116" 
                                    cx="125" 
                                    cy="125"/>
                            <circle class="progress-ring-circle" 
                                    id="progress-circle"
                                    stroke="#10b981" 
                                    stroke-width="8" 
                                    fill="transparent" 
                                    r="116" 
                                    cx="125" 
                                    cy="125"
                                    stroke-dasharray="728.3"
                                    stroke-dashoffset="728.3"/>
                        </svg>
                        
                        <!-- Counter Circle -->
                        <div class="counter-circle absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" 
                             onclick="incrementCounter()" id="counter-button">
                            <div class="counter-inner">
                                <div class="counter-number" id="counter-display">0</div>
                                <div class="text-emerald-300 text-sm">
                                    من <span id="target-count">33</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Control Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="resetCounter()" 
                                class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-redo ml-2"></i>
                            إعادة تعيين
                        </button>
                        <button onclick="toggleSound()" id="sound-btn"
                                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-volume-up ml-2"></i>
                            الصوت: مفعل
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Statistics -->
        <section class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="600">
                <h3 class="text-2xl font-bold text-center text-white mb-8">إحصائياتك اليوم</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <div class="text-3xl text-emerald-400 mb-4">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">إجمالي التسبيح</h4>
                        <p class="text-2xl font-bold text-emerald-300" id="total-count">0</p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <div class="text-3xl text-emerald-400 mb-4">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">الوقت المستغرق</h4>
                        <p class="text-2xl font-bold text-emerald-300" id="time-spent">0 دقيقة</p>
                    </div>
                    
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <div class="text-3xl text-emerald-400 mb-4">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">الجلسات المكتملة</h4>
                        <p class="text-2xl font-bold text-emerald-300" id="completed-sessions">0</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="bg-slate-800 bg-opacity-50 backdrop-blur-lg border-t border-gray-700 py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 space-x-reverse mb-4">
                    <i class="fas fa-mosque text-2xl text-emerald-400"></i>
                    <h4 class="text-2xl font-bold text-white">الذِّكر الحكيم</h4>
                </div>
                <p class="text-emerald-300 text-lg mb-6 font-light">
                    "اللهم اجعل هذا العمل خالصًا لوجهك الكريم"
                </p>
                <div class="border-t border-gray-600 pt-6">
                    <p class="text-gray-400 mb-4">
                        © 2024 الذِّكر الحكيم - جميع الحقوق محفوظة
                    </p>
                    <div class="flex items-center justify-center space-x-6 space-x-reverse">
                        <span class="text-gray-300">تطوير: Osama Developer</span>
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="mailto:<EMAIL>" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://github.com/osama" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://wa.me/1234567890" class="text-emerald-400 hover:text-emerald-300 transition-colors">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="../js/script.js"></script>
    <script>
        // Tasbih state
        let currentCount = 0;
        let targetCount = 33;
        let currentDhikr = 'سبحان الله';
        let totalDailyCount = 0;
        let completedSessions = 0;
        let startTime = Date.now();
        let soundEnabled = true;
        
        // Progress circle
        const progressCircle = document.getElementById('progress-circle');
        const circumference = 2 * Math.PI * 116; // radius = 116
        
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
            
            // Load saved data
            loadSavedData();
            updateDisplay();
            updateProgress();
        });
        
        function selectDhikr(dhikr, target) {
            currentDhikr = dhikr;
            targetCount = target;
            currentCount = 0;
            
            // Update UI
            document.getElementById('current-dhikr').textContent = dhikr;
            document.getElementById('target-count').textContent = target;
            
            // Update button states
            document.querySelectorAll('.dhikr-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.dhikr-button').classList.add('active');
            
            updateDisplay();
            updateProgress();
            saveData();
        }
        
        function incrementCounter() {
            if (currentCount < targetCount) {
                currentCount++;
                totalDailyCount++;
                
                // Add ripple effect
                addRippleEffect();
                
                // Play sound if enabled
                if (soundEnabled) {
                    playClickSound();
                }
                
                // Check if session completed
                if (currentCount === targetCount) {
                    completedSessions++;
                    showNotification(`مبروك! لقد أكملت ${currentDhikr} ${targetCount} مرة`, 'success');
                    celebrateCompletion();
                }
                
                updateDisplay();
                updateProgress();
                saveData();
            }
        }
        
        function resetCounter() {
            currentCount = 0;
            updateDisplay();
            updateProgress();
            saveData();
            showNotification('تم إعادة تعيين العداد', 'info');
        }
        
        function updateDisplay() {
            document.getElementById('counter-display').textContent = currentCount;
            document.getElementById('total-count').textContent = totalDailyCount;
            document.getElementById('completed-sessions').textContent = completedSessions;
            
            // Update time spent
            const timeSpent = Math.floor((Date.now() - startTime) / 60000);
            document.getElementById('time-spent').textContent = `${timeSpent} دقيقة`;
        }
        
        function updateProgress() {
            const progress = (currentCount / targetCount) * 100;
            const offset = circumference - (progress / 100) * circumference;
            progressCircle.style.strokeDashoffset = offset;
        }
        
        function addRippleEffect() {
            const button = document.getElementById('counter-button');
            const ripple = document.createElement('div');
            ripple.className = 'absolute inset-0 rounded-full border-4 border-emerald-400 ripple-effect pointer-events-none';
            button.appendChild(ripple);
            
            setTimeout(() => {
                button.removeChild(ripple);
            }, 600);
        }
        
        function celebrateCompletion() {
            // Add celebration animation
            const counterButton = document.getElementById('counter-button');
            counterButton.style.animation = 'pulse 0.5s ease-in-out 3';
            
            setTimeout(() => {
                counterButton.style.animation = '';
            }, 1500);
        }
        
        function toggleSound() {
            soundEnabled = !soundEnabled;
            const soundBtn = document.getElementById('sound-btn');
            
            if (soundEnabled) {
                soundBtn.innerHTML = '<i class="fas fa-volume-up ml-2"></i>الصوت: مفعل';
                soundBtn.className = soundBtn.className.replace('bg-gray-500', 'bg-blue-500');
            } else {
                soundBtn.innerHTML = '<i class="fas fa-volume-mute ml-2"></i>الصوت: مغلق';
                soundBtn.className = soundBtn.className.replace('bg-blue-500', 'bg-gray-500');
            }
            
            saveData();
        }
        
        function playClickSound() {
            // Create a simple click sound using Web Audio API
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (error) {
                // Fallback for browsers that don't support Web Audio API
                console.log('Audio not supported');
            }
        }
        
        function saveData() {
            const data = {
                currentCount,
                targetCount,
                currentDhikr,
                totalDailyCount,
                completedSessions,
                soundEnabled,
                date: new Date().toDateString()
            };
            
            localStorage.setItem('tasbihData', JSON.stringify(data));
        }
        
        function loadSavedData() {
            const saved = localStorage.getItem('tasbihData');
            if (saved) {
                const data = JSON.parse(saved);
                
                // Check if it's the same day
                if (data.date === new Date().toDateString()) {
                    currentCount = data.currentCount || 0;
                    targetCount = data.targetCount || 33;
                    currentDhikr = data.currentDhikr || 'سبحان الله';
                    totalDailyCount = data.totalDailyCount || 0;
                    completedSessions = data.completedSessions || 0;
                    soundEnabled = data.soundEnabled !== undefined ? data.soundEnabled : true;
                } else {
                    // New day, reset daily counters
                    totalDailyCount = 0;
                    completedSessions = 0;
                    startTime = Date.now();
                }
            }
        }
        
        function goToHome() {
            window.location.href = 'index.html';
        }
        
        function goBack() {
            window.history.back();
        }
        
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `fixed top-20 right-6 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
            
            if (type === 'success') {
                notification.classList.add('bg-emerald-500', 'text-white');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
            }
            
            notification.innerHTML = `
                <div class="flex items-center space-x-3 space-x-reverse">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // Keyboard support
        document.addEventListener('keydown', function(event) {
            if (event.code === 'Space') {
                event.preventDefault();
                incrementCounter();
            } else if (event.code === 'KeyR') {
                resetCounter();
            }
        });
    </script>
</body>
</html>
